#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to set up the database tables in Supabase.
Run this script to create the required tables.
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

# Initialize Supabase client
url: str = os.environ.get("SUPABASE_PROJECT_URL")
key: str = os.environ.get("SUPABASE_API_KEY")
supabase: Client = create_client(url, key)

def setup_database():
    """Set up the database tables and initial data"""
    
    print("Setting up database...")
    
    # Read the SQL file
    with open('setup_database.sql', 'r') as f:
        sql_commands = f.read()
    
    # Split into individual commands
    commands = [cmd.strip() for cmd in sql_commands.split(';') if cmd.strip()]
    
    for command in commands:
        try:
            print(f"Executing: {command[:50]}...")
            # For Supabase, we need to use the REST API or the SQL editor
            # Since we can't execute raw SQL directly, let's create tables manually
            pass
        except Exception as e:
            print(f"Error executing command: {e}")
    
    # Create moods manually
    moods = ['Happy', 'Calm', 'Motivational']
    for mood in moods:
        try:
            result = supabase.table('moods').insert({'mood_name': mood}).execute()
            print(f"Inserted mood: {mood}")
        except Exception as e:
            print(f"Mood {mood} might already exist: {e}")
    
    print("Database setup completed!")
    print("\nPlease manually create the tables in Supabase dashboard using the SQL from setup_database.sql")
    print("Then run this script again to insert the default moods.")

if __name__ == "__main__":
    setup_database()
