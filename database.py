import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load .env file if it exists (for local development)
load_dotenv()

# Initialize Supabase client
url: str = os.environ.get("SUPABASE_PROJECT_URL")
key: str = os.environ.get("SUPABASE_API_KEY")

if not url or not key:
    raise ValueError("SUPABASE_PROJECT_URL and SUPABASE_API_KEY environment variables must be set")

supabase: Client = create_client(url, key)

def create_tables():
    """Create the required tables if they don't exist"""

    try:
        # Insert default moods if they don't exist
        moods = ['Happy', 'Calm', 'Motivational']
        for mood in moods:
            try:
                # Check if mood already exists first
                existing = supabase.table('moods').select('id').eq('mood_name', mood).execute()
                if not existing.data:
                    supabase.table('moods').insert({'mood_name': mood}).execute()
                    print(f"Created mood: {mood}")
                else:
                    print(f"Mood already exists: {mood}")
            except Exception as mood_error:
                print(f"Warning creating mood '{mood}': {mood_error}")

        print("Database setup completed!")

    except Exception as e:
        print(f"Error setting up database: {e}")
        print("Please ensure the tables are created in Supabase dashboard:")

def get_supabase_client():
    """Return the Supabase client"""
    return supabase
