# Project Title: Feel Better Web Application (Hackathon MVP)

## 1. Project Overview & Goal

- **Application Name:** `Feel Better`
- **Core Idea:**  
  A web application (initially, backend to be reused for mobile) that serves as a positive alternative to traditional social media like IG that creates insecurity amongst girls and people that they are behind in life or looks. Users select their age range and a mood, and the app displays a feed of short, AI-generated videos featuring various inspirational speakers, tailored to the chosen mood, designed to be uplifting.

  Skip user registration for now. Anyone can enter and enter name, age-group, and select mood and see the feed. Obviously if the same user(identified by the same device - web/mobile if the cache is already there) opens the site again, they don't need to enter age group and name again, they will see the previous feed unless they change the mood.

- **Hackathon Goal:**  
  Demonstrate a working MVP including:
    - An admin panel (`/admin`) to onboard new speakers for *automated* AI content generation.
    - A *separate* admin panel (`/demo-admin`) to directly input *pre-computed* video data for demo purposes.
    - A user-facing feed that displays videos (both pre-computed and pipeline-generated) based on selected mood, ranked by most viewed.
    - The core backend pipeline for automated content generation.
- **Target Audience for Videos:**  
  Primarily 18-24 age group, but user can select other age ranges.

## 2. Tech Stack

- **Backend Language:** Python
- **Web Framework (for Admin & User Frontend):** Flask or FastAPI (agent's choice for simplicity, with basic HTML/JS/CSS for frontend).
- **Database:** Supabase
- **Core APIs:**
    - **Sieve:** `sieve/youtube-downloader` (for audio extraction), `sieve/lipsync` (model: `sync-2.0`)
    - **Fal.ai:** `fal-ai/minimax/voice-clone`, `fal-ai/minimax/speech-02-turbo`
    - **OpenAI:** `gpt-4o-mini` (for script generation)
- **API Key Management:** Load keys from a `.env` file.

## 3. Database Schema (Supabase)

### `speakers` Table

- `id`: SERIAL PRIMARY KEY
- `full_name`: TEXT NOT NULL
- `real_video_sample_url`: TEXT (YouTube link for pipeline, NULL if speaker is *only* pre-computed and voice is manually specified)
- `downloaded_real_audio_url`: TEXT (URL to audio extracted by Sieve, NULLable)
- `fal_custom_voice_id`: TEXT NOT NULL (ID from fal.ai voice clone API OR manually entered for pre-computed)
- `created_at`: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP

### `moods` Table

- `id`: SERIAL PRIMARY KEY
- `mood_name`: TEXT NOT NULL UNIQUE (Initially: "Happy", "Calm", "Motivational". Design for easy extension.)
- `created_at`: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP

### `speaker_mood_content` Table

- `id`: SERIAL PRIMARY KEY
- `speaker_id`: INTEGER REFERENCES speakers(id) ON DELETE CASCADE
- `mood_id`: INTEGER REFERENCES moods(id) ON DELETE CASCADE
- `admin_provided_ai_video_sample_url`: TEXT (Visual source for lip-sync, provided by admin via `/admin` or `/demo-admin`. This is the video track without audio that will be lip-synced.)
- `generated_speech_script`: TEXT (Generated by LLM OR manually entered via `/demo-admin`)
- `generated_ai_audio_url`: TEXT (URL to AI-generated audio OR manually entered via `/demo-admin`)
- `final_ai_video_url`: TEXT NOT NULL (URL to final lip-synced video, generated by pipeline OR manually entered via `/demo-admin`)
- `is_precomputed`: BOOLEAN DEFAULT FALSE (Flag set to `true` if data is entered via `/demo-admin`)
- `status_audio_extraction`: TEXT DEFAULT 'pending' ('pending', 'processing', 'completed', 'failed', 'skipped')
- `status_voice_clone`: TEXT DEFAULT 'pending' ('pending', 'processing', 'completed', 'failed', 'skipped')
- `status_script_generation`: TEXT DEFAULT 'pending' ('pending', 'processing', 'completed', 'failed', 'skipped')
- `status_ai_audio_generation`: TEXT DEFAULT 'pending' ('pending', 'processing', 'completed', 'failed', 'skipped')
- `status_final_video_generation`: TEXT DEFAULT 'pending' ('pending', 'processing', 'completed', 'failed', 'skipped')
- `error_message`: TEXT (To store any error messages during generation)
- `view_count`: INTEGER DEFAULT 0
- `created_at`: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- `updated_at`: TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
- UNIQUE (`speaker_id`, `mood_id`)

## 4. Admin Panels (Web)

### 4.1. Standard Admin Panel (`/admin` route)

- **Authentication:** Simple password protection (from `.env`).
- **Functionality: Add New Speaker (for Pipeline Generation)**
    - Form fields:
        - `Full Name` (text input)
        - `Link to Real Video Sample (YouTube)` (URL input - max 5 mins for voice cloning)
        - For each mood (dynamically from `moods` table):
            - `AI Video Sample Link for [Mood Name]` (URL input - visual source for lip-sync)
    - On submit:
        1. Create `speakers` record.
        2. For each mood, create `speaker_mood_content` record with `admin_provided_ai_video_sample_url`, `is_precomputed = false`, and all status fields to 'pending'.
        3. Trigger the asynchronous backend automation pipeline for these new `speaker_mood_content` records.

### 4.2. Pre-computed/Demo Data Admin Panel (`/demo-admin` route - *to be disabled post-hackathon*)

- **Authentication:** Simple password protection (from `.env`, can be same or different from `/admin`).
- **Functionality: Add/Edit Pre-computed Speaker Content**
    - Option to select an existing Speaker OR Add New Speaker (`Full Name`). If new, create `speakers` record.
    - For a selected/new speaker, select a Mood from `moods` table.
    - Form to directly input:
        - `Fal Custom Voice ID` (Required if not using pipeline for voice clone)
        - `Generated Speech Script` (Required)
        - `AI Video Sample Link for [Mood Name]` (Required - visual source for lip-sync)
        - `Generated AI Audio URL` (Optional - if providing pre-made audio for lip-sync. If provided, AI Audio Generation step is skipped)
        - `Final AI Video URL` (Required - link to your pre-made final video that is already lip-synced)
    - On submit:
        1. Create/Update `speakers` record (especially `fal_custom_voice_id` if entered).
        2. Create/Update `speaker_mood_content` record for the speaker/mood combination.
        3. Set `is_precomputed = true`.
        4. Populate all provided fields directly (`generated_speech_script`, `admin_provided_ai_video_sample_url`, `generated_ai_audio_url` if any, `final_ai_video_url`).
        5. Set ALL pipeline status fields (`status_audio_extraction`, `status_voice_clone`, etc.) to 'skipped' or 'completed' as appropriate, since this is manually entered data. The pipeline will NOT run for records where `is_precomputed = true`.

## 5. Backend Automation Pipeline (Python - Asynchronous)

- This pipeline is triggered for `speaker_mood_content` records where `is_precomputed = false` and relevant prerequisite steps are completed.
- Use asynchronous tasks (e.g., Python's `asyncio` with `httpx` for API calls, or Celery if time permits for robustness).
- Update status fields in the `speaker_mood_content` table after each step. Log errors to `error_message`.

### Step 0: Initial Check

For each `speaker_mood_content` record needing processing:

- If `is_precomputed == true` OR `final_ai_video_url` is already populated for this record, mark all its pipeline statuses as 'skipped' (if not already 'completed') and DO NOT PROCEED with further steps for this record.

### Step 1: Download Audio from Real Video Sample (Sieve)

- **Trigger:** `is_precomputed = false`, `status_audio_extraction = 'pending'`, `speakers.real_video_sample_url` is present.
- **API:** Sieve YouTube Downloader (`sieve/youtube-downloader`)
- **Input:** `speakers.real_video_sample_url`
- **Action:** Update `status_audio_extraction` to 'processing'. Download audio. Store it (e.g., Supabase Storage). Save accessible URL to `speakers.downloaded_real_audio_url`.
- **Output:** On success, update `status_audio_extraction` to 'completed'. On failure, to 'failed'.

### Step 2: Voice Clone (Fal.ai)

- **Trigger:** `is_precomputed = false`, `status_voice_clone = 'pending'`, `speakers.downloaded_real_audio_url` is populated, `status_audio_extraction = 'completed'`.
- **API:** Fal.ai Voice Clone (`fal-ai/minimax/voice-clone`)
- **Input:** `speakers.downloaded_real_audio_url` (ensure public S3/HTTP URL, max 5 mins).
- **Action:** Update `status_voice_clone` to 'processing'. Initiate voice cloning. Store returned `voice_id` in `speakers.fal_custom_voice_id`.
- **Output:** On success, update `status_voice_clone` to 'completed'. On failure, to 'failed'.

### Step 3: Generate Speech Scripts (GPT-4o-mini)

- **Trigger:** `is_precomputed = false`, `status_script_generation = 'pending'` for a specific `speaker_mood_content` record.
- **Model:** OpenAI `gpt-4o-mini`
- **System Prompt (Same as your good version, ensure it's fully included here for the agent):**

    ```
    You are an expert scriptwriter specializing in crafting short, impactful, and authentic-sounding monologues for inspirational figures. Your goal is to generate a script (maximum 30 seconds spoken, which is roughly 70-80 words) tailored to a specific speaker, a chosen mood (Happy, Calm, or Motivational), and targeted at an 18-24 age demographic.

    **Instructions for Script Generation:**
    1.  **Authenticity:** Before writing, use the provided web search tool to learn about the specified speaker's known speaking style, common themes, famous quotes, and overall persona. Infuse these elements naturally into the script. If the speaker is less known (e.g., a CEO like 'Sabba Keynejad from VEED'), focus on a persona that aligns with their role and the general mood.
    2.  **Mood Alignment:** The script's tone and content MUST strongly reflect the chosen mood.
        *   **Happy:** Focus on joy, gratitude, small wins, positivity, energy, passion in their field.
        *   **Calm:** Focus on peace, mindfulness, managing stress/pressure, inner strength, focus, strategic pauses.
        *   **Motivational:** Focus on action, overcoming challenges, potential, purpose, innovation, drive, achieving goals.
    3.  **Target Audience (18-24):** Use language and references that resonate with this age group without being patronizing or stereotypical. Address their likely aspirations and anxieties in a way the speaker would.
    4.  **Emphasis:** Strategically use ALL CAPS for 1-2 words per 2-3 sentences to guide the AI voice model for emotional emphasis and stress (e.g., "That's YOUR gold", "The time is NOW", "Let's GO!"). Do not overdo it.
    5.  **Conciseness:** The script MUST be speakable within 30 seconds. Aim for brevity and impact.
    6.  **Output:** Provide ONLY the script text. Do not include any other commentary, titles, or speaker names in the script itself.
    ```

- **Sample OpenAPI call**

    ```
    from openai import OpenAI
    client = OpenAI()
    response = client.responses.create(
    model="gpt-4.1-mini",
    input=[
        {
        "role": "system",
        "content": [
            {
            "type": "input_text",
            "text": "You are an expert scriptwriter specializing in crafting short, impactful, and authentic-sounding monologues for inspirational figures. Your goal is to generate a script (maximum 30 seconds spoken, which is roughly 70-80 words) tailored to a specific speaker, a chosen mood (Happy, Calm, or Motivational), and targeted at an 18-24 age demographic.\n\n**Instructions for Script Generation:**\n1.  **Authenticity:** Before writing, use the provided web search tool to learn about the specified speaker's known speaking style, common themes, famous quotes, and overall persona. Infuse these elements naturally into the script. If the speaker is less known (e.g., a CEO like 'Sabba Keynejad from VEED'), focus on a persona that aligns with their role and the general mood.\n2.  **Mood Alignment:** The script's tone and content MUST strongly reflect the chosen mood.\n    *   **Happy:** Focus on joy, gratitude, small wins, positivity, energy, passion in their field.\n    *   **Calm:** Focus on peace, mindfulness, managing stress/pressure, inner strength, focus, strategic pauses.\n    *   **Motivational:** Focus on action, overcoming challenges, potential, purpose, innovation, drive, achieving goals.\n3.  **Target Audience (18-24):** Use language and references that resonate with this age group without being patronizing or stereotypical. Address their likely aspirations and anxieties in a way the speaker would.\n4.  **Emphasis:** Strategically use ALL CAPS for 1-2 words per 2-3 sentences to guide the AI voice model for emotional emphasis and stress (e.g., \"That's YOUR gold\", \"The time is NOW\", \"Let's GO!\"). Do not overdo it.\n5.  **Conciseness:** The script MUST be speakable within 30 seconds. Aim for brevity and impact.\n6.  **Output:** Provide ONLY the script text. Do not include any other commentary, titles, or speaker names in the script itself."
            }
        ]
        },
        {
        "role": "user",
        "content": [
            {
            "type": "input_text",
            "text": "Generate a script for Emma Watson for happy mood."
            }
        ]
        },
        {
        "id": "ws_683b5c1d9fd881929bfcf99b627d7bcf0d248e33e18ed576",
        "type": "web_search_call",
        "status": "completed"
        },
        {
        "id": "msg_683b5c1eda888192915b1b95a31b50ba0d248e33e18ed576",
        "role": "assistant",
        "content": [
            {
            "type": "output_text",
            "text": "Embrace the joy in every moment. Celebrate your unique journey and the small victories along the way. Remember, happiness is found in the present, not in waiting for the perfect future. So, let's cherish today and all the possibilities it brings. "
            }
        ]
        }
    ],
    text={
        "format": {
        "type": "text"
        }
    },
    reasoning={},
    tools=[
        {
        "type": "web_search_preview",
        "user_location": {
            "type": "approximate"
        },
        "search_context_size": "medium"
        }
    ],
    temperature=1,
    max_output_tokens=2048,
    top_p=1,
    store=True
    )
    ```

- **User Prompt to LLM (example):**  
  `Generate a script for speaker '[SPEAKER_FULL_NAME]' for the mood '[MOOD_NAME]'.`
- **Action:** Update `status_script_generation` to 'processing'. Call LLM. Store script in `speaker_mood_content.generated_speech_script`.
- **Output:** On success, update `status_script_generation` to 'completed'. On failure, to 'failed'.

### Step 4: Generate AI Audio (Fal.ai)

- **Trigger:** `is_precomputed = false`, `status_ai_audio_generation = 'pending'`, `status_script_generation = 'completed'`, `speakers.fal_custom_voice_id` is populated.
- **API:** Fal.ai Speech Synthesis (`fal-ai/minimax/speech-02-turbo`)
- **Input:** `text` (from `generated_speech_script`), `voice_id` (from `speakers.fal_custom_voice_id`), `emotion` ("Happy" for Happy mood, "Default" for Calm/Motivational).
- **Action:** Update `status_ai_audio_generation` to 'processing'. Generate audio. Store it (Supabase Storage). Save URL to `speaker_mood_content.generated_ai_audio_url`.
- **Output:** On success, update `status_ai_audio_generation` to 'completed'. On failure, to 'failed'.

### Step 5: Create Final AI Video (Sieve Lip Sync)

- **Trigger:** `is_precomputed = false`, `status_final_video_generation = 'pending'`, `status_ai_audio_generation = 'completed'`, `speaker_mood_content.admin_provided_ai_video_sample_url` is populated.
- **API:** Sieve Lip Sync (`sieve/lipsync`)
- **Input:** `video_url` (from `admin_provided_ai_video_sample_url`), `audio_url` (from `generated_ai_audio_url`), `model_name: 'sync-2.0'`, `multi_speaker: false`.
- **Action:** Update `status_final_video_generation` to 'processing'. Generate lip-synced video. Store it (Supabase Storage). Save URL to `speaker_mood_content.final_ai_video_url`.
- **Output:** On success, update `status_final_video_generation` to 'completed'. On failure, to 'failed'.

## 6. User-Facing Web Application (Frontend - `/` route)

- **Initial Input:**
    - User selects Age Range (dropdown: ` tag.
- **Mood Changer:** Clear and easy way for the user to select a different mood, which refreshes the feed.
- **View Tracking:** When a video plays for >3 seconds, make a POST request to `/api/video/increment_view/` to update its view count.

## 7. Backend API Endpoints (Python - Flask/FastAPI)

- `/api/moods`: GET - Returns `[{id, mood_name}, ...]`.
- `/api/feed?mood_id=`: GET -
    - Fetches `speaker_mood_content` records joined with `speakers.full_name`.
    - Filters by the provided `mood_id`.
    - Filters where `final_ai_video_url` IS NOT NULL AND (`is_precomputed = true` OR `status_final_video_generation = 'completed'`).
    - Orders by `view_count` DESC.
    - Returns JSON:  
      `[{ "content_id": speaker_mood_content.id, "speaker_name": speakers.full_name, "final_video_url": speaker_mood_content.final_ai_video_url, "view_count": speaker_mood_content.view_count }, ...]`.
- `/api/video/increment_view/`: POST -
    - Takes `content_id` (which is `speaker_mood_content.id`) from the path.
    - Increments the `view_count` for that record in the `speaker_mood_content` table.
    - Returns success/failure status.


## 8. Error Handling & Logging

- **Pipeline Steps:**  
  - Every pipeline step (audio extraction, voice cloning, script generation, AI audio, lip sync) must:
    - Update the relevant `status_*` field to `'processing'` when started.
    - On error, update the relevant `status_*` field to `'failed'`.
    - Write a clear, concise error message to the `error_message` field in `speaker_mood_content`.
    - The pipeline should **NOT** proceed to the next step if the previous step failed.
    - If a record is re-queued or retried, pipeline should check if previous errors have been resolved.
- **Admin Panel:**  
  - The `/admin` panel should show the status and error message for each `speaker_mood_content` entry.
  - Admin should be able to manually reset a failed step to `'pending'` to trigger a retry.
- **API Endpoints:**  
  - All API endpoints must return appropriate HTTP status codes:
    - `200` for success.
    - `400` for bad requests (e.g., missing parameters).
    - `404` for not found.
    - `500` for unexpected errors.
  - Error messages should be included in the JSON response under an `error` field.
- **Logging:**  
  - All backend errors and important events should be logged to console and/or a file (use Python’s `logging` module).
  - For production, consider integrating with a service like Sentry for error monitoring.

---

## 9. Deployment & Operations

- **Environment Variables:**  
  - Store all secrets (API keys, DB credentials, admin passwords) in a `.env` file.
- **Deployment:**  
  - Use Docker for containerization (optional for hackathon, recommended for production).
  - Deploy to a cloud provider (e.g., Vercel, Heroku, AWS, or Supabase Edge Functions).
- **Supabase:**  
  - Use Supabase for database and storage.
  - Ensure storage buckets are set to public or signed URLs as needed for video/audio access.
- **Scaling:**  
  - The backend should be stateless and horizontally scalable.
  - Asynchronous pipeline tasks should be managed by a queue (Celery, or simple async workers).

---

## 10. Monitoring & Analytics

- **Pipeline Monitoring:**  
  - Admin panel should display the status of all pipeline jobs, including failed, pending, and completed.
  - Optionally, send email or Slack notifications to admins on pipeline failures.
- **User Analytics:**  
  - Track video views, mood selections, and age ranges (anonymously).
  - Store aggregate analytics in a separate table for reporting.
- **Error Monitoring:**  
  - Integrate with Sentry or similar for real-time error tracking.

---

## 11. Security & Privacy

- **Authentication:**  
  - Admin routes (`/admin`, `/demo-admin`) must be password protected.
  - User-facing routes do **not** require login.
- **Data Privacy:**  
  - Do not store any personally identifiable information (PII) from users.
  - All analytics should be anonymous and aggregated.
- **API Security:**  
  - Validate all input to API endpoints.
  - Rate-limit sensitive endpoints to prevent abuse.

---

## 12. Notes

- **Demo Mode:**  
  - `/demo-admin` is for hackathon/demo only. Disable or secure it in production.

---


# Resources
1. Obtain audio from video: https://www.sievedata.com/functions/sieve/youtube-downloader/guide

2. Voice cloning : https://fal.ai/models/fal-ai/minimax/voice-clone/api

3. Text to speech API : https://fal.ai/models/fal-ai/minimax/speech-02-turbo/api

4. Lip sync by Seive - https://www.sievedata.com/functions/sieve/lipsync
