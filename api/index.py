import sys
import os

# Fix for Python 3.12 compatibility - patch collections module before any other imports
import collections
import collections.abc

# Patch missing attributes that were moved to collections.abc in Python 3.3+
# and removed from collections in Python 3.12
for attr_name in ['Sequence', 'Mapping', 'MutableMapping', 'Iterable', 'Iterator',
                  'Callable', 'Set', 'MutableSet', 'Container', 'Sized', 'Hashable']:
    if not hasattr(collections, attr_name):
        if hasattr(collections.abc, attr_name):
            setattr(collections, attr_name, getattr(collections.abc, attr_name))

# Fix for PosixPath read_text compatibility
import pathlib
if not hasattr(pathlib.PosixPath, 'read_text'):
    def read_text(self, encoding='utf-8', errors='strict'):
        with open(self, 'r', encoding=encoding, errors=errors) as f:
            return f.read()
    pathlib.PosixPath.read_text = read_text

# Also patch WindowsPath for completeness
if hasattr(pathlib, 'WindowsPath') and not hasattr(pathlib.WindowsPath, 'read_text'):
    def read_text(self, encoding='utf-8', errors='strict'):
        with open(self, 'r', encoding=encoding, errors=errors) as f:
            return f.read()
    pathlib.WindowsPath.read_text = read_text

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    print("Attempting to import main app...")
    from app import app
    print("Successfully imported Flask app")

except Exception as e:
    import traceback
    error_message = str(e)
    error_traceback = traceback.format_exc()
    print(f"Import error: {error_message}")
    print(f"Full traceback: {error_traceback}")

    # Create a minimal error app
    from flask import Flask, jsonify
    app = Flask(__name__)

    @app.route('/')
    def error():
        return jsonify({
            'error': 'Application failed to initialize',
            'details': error_message,
            'traceback': error_traceback
        }), 500

    @app.route('/favicon.ico')
    def favicon():
        return '', 204

# For Vercel, we need to export the app
# FORCE REBUILD - Version 2.0
if __name__ == "__main__":
    app.run()
