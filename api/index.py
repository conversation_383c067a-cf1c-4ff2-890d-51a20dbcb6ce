import collections.abc
import sys
import os

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from app import app
    print("Successfully imported Flask app")

except Exception as e:
    print(f"Import error: {e}")
    # Create a minimal error app
    from flask import Flask, jsonify
    app = Flask(__name__)

    @app.route('/')
    def error():
        return jsonify({'error': 'Application failed to initialize', 'details': str(e)}), 500

    @app.route('/favicon.ico')
    def favicon():
        return '', 204

# For Vercel, we need to export the app
if __name__ == "__main__":
    app.run()
