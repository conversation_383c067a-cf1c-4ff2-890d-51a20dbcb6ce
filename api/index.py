import sys
import os

# Fix for Python 3.12 compatibility - patch collections module before any other imports
import collections
import collections.abc

# Patch missing attributes that were moved to collections.abc in Python 3.3+
# and removed from collections in Python 3.12
for attr_name in ['Sequence', 'Mapping', 'MutableMapping', 'Iterable', 'Iterator',
                  'Callable', 'Set', 'MutableSet', 'Container', 'Sized', 'Hashable']:
    if not hasattr(collections, attr_name):
        if hasattr(collections.abc, attr_name):
            setattr(collections, attr_name, getattr(collections.abc, attr_name))

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from app import app
    print("Successfully imported Flask app")

except Exception as e:
    error_message = str(e)
    print(f"Import error: {error_message}")
    # Create a minimal error app
    from flask import Flask, jsonify
    app = Flask(__name__)

    @app.route('/')
    def error():
        return jsonify({'error': 'Application failed to initialize', 'details': error_message}), 500

    @app.route('/favicon.ico')
    def favicon():
        return '', 204

# For Vercel, we need to export the app
if __name__ == "__main__":
    app.run()
