import os
import asyncio
import httpx
import fal_client
import sieve
from openai import OpenAI
from database import get_supabase_client
import time
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize clients
supabase = get_supabase_client()
openai_client = OpenAI(api_key=os.environ.get('OPENAI_API_KEY'))

# API Keys
SIEVE_API_KEY = os.environ.get('SIEVE_API_KEY')
FAL_API_KEY = os.environ.get('FAL_API_KEY')

def trigger_pipeline(speaker_id):
    """Trigger the content generation pipeline for a speaker"""
    try:
        asyncio.run(run_pipeline(speaker_id))
    except Exception as e:
        logger.error(f"Pipeline error for speaker {speaker_id}: {e}")

async def run_pipeline(speaker_id):
    """Run the complete pipeline for a speaker"""
    logger.info(f"Starting pipeline for speaker {speaker_id}")
    
    # Get speaker info
    speaker_response = supabase.table('speakers').select('*').eq('id', speaker_id).execute()
    if not speaker_response.data:
        logger.error(f"Speaker {speaker_id} not found")
        return
    
    speaker = speaker_response.data[0]
    
    # Step 1: Download audio from video (if needed)
    if speaker['real_video_sample_url'] and not speaker['downloaded_real_audio_url']:
        await download_audio(speaker_id, speaker['real_video_sample_url'])
        # Refresh speaker data
        speaker_response = supabase.table('speakers').select('*').eq('id', speaker_id).execute()
        speaker = speaker_response.data[0]

    # Step 1.5: Download video file (if needed)
    if speaker['real_video_sample_url'] and not speaker.get('downloaded_real_video_url'):
        await download_video(speaker_id, speaker['real_video_sample_url'])
        # Refresh speaker data
        speaker_response = supabase.table('speakers').select('*').eq('id', speaker_id).execute()
        speaker = speaker_response.data[0]
    
    # Step 2: Voice clone (if needed)
    if speaker['downloaded_real_audio_url'] and not speaker['fal_custom_voice_id']:
        await voice_clone(speaker_id, speaker['downloaded_real_audio_url'])
        # Refresh speaker data
        speaker_response = supabase.table('speakers').select('*').eq('id', speaker_id).execute()
        speaker = speaker_response.data[0]
    
    # Step 3: Process each mood content
    content_response = supabase.table('speaker_mood_content').select('*').eq('speaker_id', speaker_id).eq('is_precomputed', False).execute()
    
    for content in content_response.data:
        await process_content(content, speaker)

async def download_audio(speaker_id, video_url):
    """Download audio from YouTube video using Sieve"""
    try:
        # Update status
        supabase.table('speaker_mood_content').update({
            'status_audio_extraction': 'processing'
        }).eq('speaker_id', speaker_id).execute()
        
        logger.info(f"Downloading audio for speaker {speaker_id}")
        
        # Call Sieve API
        async with httpx.AsyncClient() as client:
            response = await client.post(
                'https://mango.sievedata.com/v2/push',
                headers={'X-API-Key': SIEVE_API_KEY},
                json={
                    'function': 'sieve/youtube-downloader',
                    'inputs': {
                        'url': video_url,
                        'download_type': 'audio',
                        'resolution': 'highest-available',
                        'include_audio': True,
                        'start_time': 0,
                        'end_time': -1,
                        'include_metadata': False,
                        'metadata_fields': [
                            'title',
                            'thumbnail',
                            'description',
                            'tags',
                            'duration'
                        ],
                        'include_subtitles': False,
                        'subtitle_languages': ['en'],
                        'video_format': 'mp4',
                        'audio_format': 'mp3',
                        'subtitle_format': 'vtt'
                    }
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                job_id = result.get('id')
                
                # Poll for completion
                audio_url = await poll_sieve_job(job_id, job_type="audio_download")
                
                if audio_url:
                    # Update speaker with audio URL
                    supabase.table('speakers').update({
                        'downloaded_real_audio_url': audio_url
                    }).eq('id', speaker_id).execute()
                    
                    # Update status
                    supabase.table('speaker_mood_content').update({
                        'status_audio_extraction': 'completed'
                    }).eq('speaker_id', speaker_id).execute()
                    
                    logger.info(f"Audio download completed for speaker {speaker_id}")
                else:
                    raise Exception("Failed to get audio URL from Sieve")
            else:
                raise Exception(f"Sieve API error: {response.text}")
                
    except Exception as e:
        logger.error(f"Audio download failed for speaker {speaker_id}: {e}")
        supabase.table('speaker_mood_content').update({
            'status_audio_extraction': 'failed',
            'error_message': str(e)
        }).eq('speaker_id', speaker_id).execute()

async def download_video(speaker_id, video_url):
    """Download video from YouTube using Sieve"""
    try:
        logger.info(f"Downloading video for speaker {speaker_id}")

        # Call Sieve API for video download
        async with httpx.AsyncClient() as client:
            response = await client.post(
                'https://mango.sievedata.com/v2/push',
                headers={'X-API-Key': SIEVE_API_KEY},
                json={
                    'function': 'sieve/youtube-downloader',
                    'inputs': {
                        'url': video_url,
                        'download_type': 'video',
                        'resolution': 'highest-available',
                        'include_audio': True,
                        'start_time': 0,
                        'end_time': -1,
                        'include_metadata': False,
                        'metadata_fields': [
                            'title',
                            'thumbnail',
                            'description',
                            'tags',
                            'duration'
                        ],
                        'include_subtitles': False,
                        'subtitle_languages': ['en'],
                        'video_format': 'mp4',
                        'audio_format': 'mp3',
                        'subtitle_format': 'vtt'
                    }
                }
            )

            if response.status_code == 200:
                result = response.json()
                job_id = result.get('id')

                # Poll for completion
                video_url_downloaded = await poll_sieve_job(job_id, job_type="video_download")

                if video_url_downloaded:
                    # Update speaker with video URL
                    supabase.table('speakers').update({
                        'downloaded_real_video_url': video_url_downloaded
                    }).eq('id', speaker_id).execute()

                    logger.info(f"Video download completed for speaker {speaker_id}")
                else:
                    raise Exception("Failed to get video URL from Sieve")
            else:
                raise Exception(f"Sieve API error: {response.text}")

    except Exception as e:
        logger.error(f"Video download failed for speaker {speaker_id}: {e}")

async def voice_clone(speaker_id, audio_url):
    """Clone voice using Fal.ai"""
    try:
        logger.info(f"Cloning voice for speaker {speaker_id}")

        # Update status
        supabase.table('speaker_mood_content').update({
            'status_voice_clone': 'processing'
        }).eq('speaker_id', speaker_id).execute()

        # Call Fal.ai voice clone using async API
        handler = await fal_client.submit_async(
            "fal-ai/minimax/voice-clone",
            arguments={"audio_url": audio_url, "noise_reduction": True, "accuracy": 0.5}
        )

        # Wait for completion
        result = await handler.get()
        
        voice_id = result.get('custom_voice_id')
        if voice_id:
            # Update speaker with voice ID
            supabase.table('speakers').update({
                'fal_custom_voice_id': voice_id
            }).eq('id', speaker_id).execute()
            
            # Update status
            supabase.table('speaker_mood_content').update({
                'status_voice_clone': 'completed'
            }).eq('speaker_id', speaker_id).execute()
            
            logger.info(f"Voice clone completed for speaker {speaker_id}")
        else:
            raise Exception("Failed to get voice ID from Fal.ai")
            
    except Exception as e:
        logger.error(f"Voice clone failed for speaker {speaker_id}: {e}")
        supabase.table('speaker_mood_content').update({
            'status_voice_clone': 'failed',
            'error_message': str(e)
        }).eq('speaker_id', speaker_id).execute()

async def process_content(content, speaker):
    """Process individual content (script generation, audio generation, video generation)"""
    content_id = content['id']
    
    # Step 3: Generate script
    if content['status_script_generation'] == 'pending':
        await generate_script(content_id, speaker['full_name'], content['mood_id'])
        # Refresh content data
        content_response = supabase.table('speaker_mood_content').select('*').eq('id', content_id).execute()
        content = content_response.data[0]
    
    # Step 4: Generate AI audio
    if (content['status_ai_audio_generation'] == 'pending' and 
        content['status_script_generation'] == 'completed' and 
        speaker['fal_custom_voice_id']):
        await generate_ai_audio(content_id, content['generated_speech_script'], speaker['fal_custom_voice_id'])
        # Refresh content data
        content_response = supabase.table('speaker_mood_content').select('*').eq('id', content_id).execute()
        content = content_response.data[0]
    
    # Step 5: Generate final video
    if (content['status_final_video_generation'] == 'pending' and
        content['status_ai_audio_generation'] == 'completed' and
        content['generated_ai_audio_url']):
        # Use downloaded video URL if available, otherwise fall back to admin-provided URL
        video_url = speaker.get('downloaded_real_video_url') or content['admin_provided_ai_video_sample_url']
        if video_url:
            await generate_final_video(content_id, video_url, content['generated_ai_audio_url'])

async def generate_script(content_id, speaker_name, mood_id):
    """Generate script using OpenAI"""
    try:
        logger.info(f"Generating script for content {content_id}")

        # Update status
        supabase.table('speaker_mood_content').update({
            'status_script_generation': 'processing'
        }).eq('id', content_id).execute()

        # Get mood name
        mood_response = supabase.table('moods').select('mood_name').eq('id', mood_id).execute()
        mood_name = mood_response.data[0]['mood_name']
        
        # System prompt
        system_prompt = """You are an expert scriptwriter specializing in crafting short, impactful, and authentic-sounding monologues for inspirational figures. Your goal is to generate a script (maximum 30 seconds spoken, which is roughly 70-80 words) tailored to a specific speaker, a chosen mood (Happy, Calm, or Motivational), and targeted at an 18-24 age demographic.

**Instructions for Script Generation:**
1. **Authenticity:** Write in a style that matches the speaker's known persona and speaking style.
2. **Mood Alignment:** The script's tone and content MUST strongly reflect the chosen mood.
   * **Happy:** Focus on joy, gratitude, small wins, positivity, energy, passion in their field.
   * **Calm:** Focus on peace, mindfulness, managing stress/pressure, inner strength, focus, strategic pauses.
   * **Motivational:** Focus on action, overcoming challenges, potential, purpose, innovation, drive, achieving goals.
3. **Target Audience (18-24):** Use language and references that resonate with this age group.
4. **Emphasis:** Strategically use ALL CAPS for 1-2 words per 2-3 sentences for emotional emphasis.
5. **Conciseness:** The script MUST be speakable within 30 seconds. Aim for brevity and impact.
6. **Output:** Provide ONLY the script text."""
        
        # Generate script
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Generate a script for speaker '{speaker_name}' for the mood '{mood_name}'."}
            ],
            max_tokens=150,
            temperature=0.8
        )
        
        script = response.choices[0].message.content.strip()
        
        # Update content with script
        supabase.table('speaker_mood_content').update({
            'generated_speech_script': script,
            'status_script_generation': 'completed'
        }).eq('id', content_id).execute()
        
        logger.info(f"Script generation completed for content {content_id}")
        
    except Exception as e:
        logger.error(f"Script generation failed for content {content_id}: {e}")
        supabase.table('speaker_mood_content').update({
            'status_script_generation': 'failed',
            'error_message': str(e)
        }).eq('id', content_id).execute()

async def generate_ai_audio(content_id, script, voice_id):
    """Generate AI audio using Fal.ai"""
    try:
        logger.info(f"Generating AI audio for content {content_id}")

        # Update status
        supabase.table('speaker_mood_content').update({
            'status_ai_audio_generation': 'processing'
        }).eq('id', content_id).execute()

        # Note: The new speech-02-turbo API uses voice_setting structure instead of emotion parameter

        # Call Fal.ai speech synthesis with correct voice_setting structure using async API
        handler = await fal_client.submit_async(
            "fal-ai/minimax/speech-02-turbo",
            arguments={
                "text": script,
                "voice_setting": {
                    "speed": 1,
                    "vol": 1,
                    "voice_id": "Default",  # Default voice name
                    "pitch": 0,
                    "english_normalization": False,
                    "custom_voice_id": voice_id  # This is the key fix - custom voice ID goes here
                },
                "output_format": "hex",
                "pronunciation_dict": {
                    "tone_list": []
                }
            }
        )

        # Wait for completion
        result = await handler.get()
        
        # Extract audio URL from nested structure
        audio_url = None
        if 'audio' in result and isinstance(result['audio'], dict):
            audio_url = result['audio'].get('url')

        if audio_url:
            # Update content with audio URL
            supabase.table('speaker_mood_content').update({
                'generated_ai_audio_url': audio_url,
                'status_ai_audio_generation': 'completed'
            }).eq('id', content_id).execute()
            
            logger.info(f"AI audio generation completed for content {content_id}")
        else:
            raise Exception("Failed to get audio URL from Fal.ai")
            
    except Exception as e:
        logger.error(f"AI audio generation failed for content {content_id}: {e}")
        supabase.table('speaker_mood_content').update({
            'status_ai_audio_generation': 'failed',
            'error_message': str(e)
        }).eq('id', content_id).execute()

async def generate_final_video(content_id, video_url, audio_url):
    """Generate final lip-synced video using Sieve Python client or HTTP API fallback"""
    try:
        logger.info(f"Generating final video for content {content_id}")

        # Update status
        supabase.table('speaker_mood_content').update({
            'status_final_video_generation': 'processing'
        }).eq('id', content_id).execute()

        # Use Sieve Python client for lipsync
        lipsync_func = sieve.function.get("sieve/lipsync")
        
        sieve_video_file_input = sieve.File(url=video_url)
        sieve_audio_file_input = sieve.File(url=audio_url)

        logger.info(f"Pushing lipsync job for content {content_id} with video: {video_url}, audio: {audio_url}")

        future = lipsync_func.push(
            file=sieve_video_file_input,
            audio=sieve_audio_file_input,
            backend="sync-2.0",
            enable_multispeaker=False,
            enhance="default",
            check_quality=True,
            downsample=False,
            cut_by="audio"
        )
        
        logger.info(f"Lipsync job submitted for content {content_id}, waiting for result...")
        
        # Wait for the result in a non-blocking way
        output_result = await asyncio.to_thread(future.result)
        
        if output_result and isinstance(output_result, tuple) and len(output_result) > 0:
            sieve_output_video_file = output_result[0]
            if isinstance(sieve_output_video_file, sieve.File) and hasattr(sieve_output_video_file, 'path'):
                final_video_url = sieve_output_video_file.path # .path usually contains the URL for output files
                
                supabase.table('speaker_mood_content').update({
                    'final_ai_video_url': final_video_url,
                    'status_final_video_generation': 'completed'
                }).eq('id', content_id).execute()
                
                logger.info(f"Final video generation completed for content {content_id}. Video URL: {final_video_url}")
            else:
                logger.error(f"Lipsync job for content {content_id} finished, but output format is unexpected or missing video file path: {sieve_output_video_file}")
                raise Exception("Sieve lipsync job finished, but output video file path not found.")
        else:
            logger.error(f"Lipsync job for content {content_id} did not return expected output. Result: {output_result}")
            raise Exception("Sieve lipsync job did not return expected output.")
            
    except Exception as e:
        logger.error(f"Final video generation failed for content {content_id}: {e}")
        supabase.table('speaker_mood_content').update({
            'status_final_video_generation': 'failed',
            'error_message': str(e)
        }).eq('id', content_id).execute()

async def poll_sieve_job(job_id, max_wait=600, job_type="unknown"):
    """Poll Sieve job until completion with adaptive polling intervals"""
    start_time = time.time()
    poll_count = 0

    # Adaptive polling intervals based on job type
    if job_type == "lipsync":
        # Lipsync jobs take longer, use longer intervals
        intervals = [10, 15, 20, 30, 45, 60]  # Start at 10s, increase to 60s
    else:
        # Audio/video download jobs are faster
        intervals = [5, 10, 15, 20, 30]  # Start at 5s, increase to 30s

    async with httpx.AsyncClient() as client:
        while time.time() - start_time < max_wait:
            response = await client.get(
                f'https://mango.sievedata.com/v2/jobs/{job_id}',
                headers={'X-API-Key': SIEVE_API_KEY}
            )

            if response.status_code == 200:
                result = response.json()
                status = result.get('status')

                if status == 'finished':
                    outputs = result.get('outputs', [])
                    logger.info(f"Sieve job outputs: {outputs} (type: {type(outputs)})")

                    # Handle both list and dict outputs
                    if isinstance(outputs, list):
                        # If outputs is a list, look for URLs in the list items
                        for item in outputs:
                            logger.info(f"Checking list item: {item} (type: {type(item)})")
                            if isinstance(item, str) and (item.startswith('http') or item.startswith('gs://')):
                                return item
                            elif isinstance(item, dict):
                                # Check for Sieve File format: item['data']['url']
                                if 'data' in item and isinstance(item['data'], dict) and 'url' in item['data']:
                                    url = item['data']['url']
                                    if isinstance(url, str) and (url.startswith('http') or url.startswith('gs://')):
                                        return url
                                # If list contains dicts, check dict values recursively
                                for key, value in item.items():
                                    if isinstance(value, str) and (value.startswith('http') or value.startswith('gs://')):
                                        return value
                                    elif isinstance(value, dict) and 'url' in value:
                                        url = value['url']
                                        if isinstance(url, str) and (url.startswith('http') or url.startswith('gs://')):
                                            return url
                    elif isinstance(outputs, dict):
                        # If outputs is a dict, look for URLs in the values
                        for key, value in outputs.items():
                            if isinstance(value, str) and (value.startswith('http') or value.startswith('gs://')):
                                return value

                    logger.error(f"No URL found in Sieve outputs: {outputs}")
                    return None
                elif status == 'failed':
                    logger.error(f"Sieve job {job_id} failed: {result.get('error')}")
                    return None

                # Adaptive wait before polling again
                current_interval = intervals[min(poll_count, len(intervals) - 1)]
                logger.info(f"Sieve job {job_id} still {status}, waiting {current_interval}s before next poll (poll #{poll_count + 1})")
                await asyncio.sleep(current_interval)
                poll_count += 1
            else:
                logger.error(f"Failed to poll Sieve job {job_id}: {response.text}")
                return None
    
    logger.error(f"Sieve job {job_id} timed out")
    return None
