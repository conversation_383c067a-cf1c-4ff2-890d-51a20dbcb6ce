// Global variables
let currentMoodId = null;
let userName = '';
let ageRange = '';
let moods = [];

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    loadMoods();
    checkExistingUser();
});

// Load moods from API
async function loadMoods() {
    try {
        const response = await fetch('/api/moods');
        moods = await response.json();
        
        // Populate mood dropdown in user form
        const moodSelect = document.getElementById('mood');
        moods.forEach(mood => {
            const option = document.createElement('option');
            option.value = mood.id;
            option.textContent = mood.mood_name;
            moodSelect.appendChild(option);
        });
        
        // Create mood buttons for feed section
        createMoodButtons();
        
    } catch (error) {
        console.error('Error loading moods:', error);
    }
}

// Create mood buttons for the feed section
function createMoodButtons() {
    const moodButtonsContainer = document.getElementById('mood-buttons');
    moodButtonsContainer.innerHTML = '';
    
    moods.forEach(mood => {
        const button = document.createElement('div');
        button.className = 'mood-button';
        button.textContent = mood.mood_name;
        button.dataset.moodId = mood.id;
        button.addEventListener('click', () => changeMood(mood.id));
        moodButtonsContainer.appendChild(button);
    });
}

// Check if user data exists in localStorage
function checkExistingUser() {
    const savedUserData = localStorage.getItem('feelBetterUser');
    if (savedUserData) {
        const userData = JSON.parse(savedUserData);
        userName = userData.name;
        ageRange = userData.ageRange;
        currentMoodId = userData.lastMoodId;
        
        // Show feed directly
        showFeed();
        if (currentMoodId) {
            loadFeed(currentMoodId);
        }
    }
}

// Handle user form submission
document.getElementById('user-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    userName = document.getElementById('name').value;
    ageRange = document.getElementById('age-range').value;
    currentMoodId = document.getElementById('mood').value;
    
    // Save user data to localStorage
    const userData = {
        name: userName,
        ageRange: ageRange,
        lastMoodId: currentMoodId
    };
    localStorage.setItem('feelBetterUser', JSON.stringify(userData));
    
    // Show feed
    showFeed();
    loadFeed(currentMoodId);
});

// Show the feed section and hide user input
function showFeed() {
    document.getElementById('user-input').style.display = 'none';
    document.getElementById('feed-section').style.display = 'block';
    
    // Update active mood button
    updateActiveMoodButton();
}

// Update active mood button
function updateActiveMoodButton() {
    const moodButtons = document.querySelectorAll('.mood-button');
    moodButtons.forEach(button => {
        button.classList.remove('active');
        if (button.dataset.moodId == currentMoodId) {
            button.classList.add('active');
        }
    });
}

// Change mood
function changeMood(moodId) {
    currentMoodId = moodId;
    
    // Update localStorage
    const userData = JSON.parse(localStorage.getItem('feelBetterUser'));
    userData.lastMoodId = moodId;
    localStorage.setItem('feelBetterUser', JSON.stringify(userData));
    
    // Update UI and load feed
    updateActiveMoodButton();
    loadFeed(moodId);
}

// Load feed for specific mood
async function loadFeed(moodId) {
    const loadingElement = document.getElementById('loading');
    const videosContainer = document.getElementById('videos-container');
    
    // Show loading
    loadingElement.style.display = 'block';
    videosContainer.innerHTML = '';
    
    try {
        const response = await fetch(`/api/feed?mood_id=${moodId}`);
        const feedData = await response.json();
        
        // Hide loading
        loadingElement.style.display = 'none';
        
        if (feedData.length === 0) {
            videosContainer.innerHTML = '<div class="no-content">No content available for this mood yet. Check back soon!</div>';
            return;
        }
        
        // Display videos
        feedData.forEach(video => {
            createVideoCard(video);
        });
        
    } catch (error) {
        console.error('Error loading feed:', error);
        loadingElement.style.display = 'none';
        videosContainer.innerHTML = '<div class="error">Error loading content. Please try again.</div>';
    }
}

// Create video card
function createVideoCard(video) {
    const videosContainer = document.getElementById('videos-container');
    
    const videoCard = document.createElement('div');
    videoCard.className = 'video-card';
    
    videoCard.innerHTML = `
        <div class="video-wrapper">
            <video controls preload="metadata" data-content-id="${video.content_id}">
                <source src="${video.final_video_url}" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        </div>
        <div class="video-info">
            <h4>${video.speaker_name}</h4>
            <div class="view-count">${video.view_count} views</div>
        </div>
    `;

    // Add event listener for view tracking
    const videoElement = videoCard.querySelector('video');
    let viewTracked = false;
    let thumbnailCaptured = false;

    // Generate thumbnail by seeking to 5 seconds, capturing frame, then resetting
    videoElement.addEventListener('loadedmetadata', function() {
        if (!thumbnailCaptured) {
            // Set the current time to 5 seconds to capture thumbnail
            this.currentTime = 5;
        }
    });

    // When seeking is complete, capture the frame and reset to beginning
    videoElement.addEventListener('seeked', function() {
        if (!thumbnailCaptured && Math.abs(this.currentTime - 5) < 0.5) {
            thumbnailCaptured = true;

            // Create a canvas to capture the frame at 5 seconds
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = this.videoWidth;
            canvas.height = this.videoHeight;

            // Draw the current frame to canvas
            ctx.drawImage(this, 0, 0, canvas.width, canvas.height);

            // Convert canvas to data URL and set as poster
            const posterDataURL = canvas.toDataURL('image/jpeg', 0.8);
            this.poster = posterDataURL;

            // Reset to beginning immediately so player shows 0:00
            this.currentTime = 0;
            this.pause(); // Ensure it's paused at 0:00
        }
    });

    // Track views when video plays for more than 3 seconds
    videoElement.addEventListener('timeupdate', function() {
        if (!viewTracked && this.currentTime > 3) {
            viewTracked = true;
            incrementViewCount(video.content_id);
        }
    });
    
    videosContainer.appendChild(videoCard);
}

// Increment view count
async function incrementViewCount(contentId) {
    try {
        await fetch(`/api/video/increment_view/${contentId}`, {
            method: 'POST'
        });
    } catch (error) {
        console.error('Error incrementing view count:', error);
    }
}

// Reset user data (for testing)
function resetUserData() {
    localStorage.removeItem('feelBetterUser');
    location.reload();
}

// Add reset button for testing (can be removed in production)
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    const resetButton = document.createElement('button');
    resetButton.textContent = 'Reset User Data (Dev)';
    resetButton.style.position = 'fixed';
    resetButton.style.top = '10px';
    resetButton.style.right = '10px';
    resetButton.style.zIndex = '1000';
    resetButton.style.padding = '5px 10px';
    resetButton.style.fontSize = '12px';
    resetButton.style.width = 'auto';
    resetButton.addEventListener('click', resetUserData);
    document.body.appendChild(resetButton);
}
