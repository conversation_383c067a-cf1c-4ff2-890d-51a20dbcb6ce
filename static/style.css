* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* User Input Section */
.user-input {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 40px;
}

.user-input h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #667eea;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 14px;
}

button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s;
    width: 100%;
}

button:hover {
    transform: translateY(-2px);
}

/* Feed Section */
.feed-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.mood-selector {
    margin-bottom: 30px;
    text-align: center;
}

.mood-selector h3 {
    margin-bottom: 15px;
    color: #667eea;
}

.mood-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.mood-button {
    padding: 10px 20px;
    background: #f0f0f0;
    border: 2px solid #ddd;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: bold;
}

.mood-button:hover,
.mood-button.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Video Feed */
.videos-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    padding: 16px 0;
}

.video-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255,255,255,0.2);
}

.video-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
}

.video-wrapper {
    position: relative;
    width: 100%;
    background: #000;
}

.video-card video {
    width: 100%;
    height: auto;
    max-height: 300px;
    min-height: 180px;
    object-fit: contain;
    object-position: center;
    background-color: #000;
    display: block;
}

.video-info {
    padding: 12px 16px;
    background: white;
}

.video-info h4 {
    margin: 0 0 6px 0;
    color: #2d3748;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.3;
}

.video-info .view-count {
    color: #718096;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.video-info .view-count::before {
    content: "👁";
    font-size: 11px;
}

.loading {
    text-align: center;
    padding: 40px;
    font-size: 18px;
    color: #667eea;
}

/* Admin Styles */
.admin-login {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    max-width: 400px;
    margin: 100px auto;
}

.admin-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.admin-section h2 {
    color: #667eea;
    margin-bottom: 20px;
}

.speaker-selection {
    display: flex;
    gap: 10px;
    align-items: center;
}

.speaker-selection select,
.speaker-selection input {
    flex: 1;
}

.speakers-list {
    display: grid;
    gap: 20px;
}

.speaker-card {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #ddd;
}

.speaker-card h3 {
    color: #667eea;
    margin-bottom: 10px;
}

.content-status {
    margin-top: 15px;
}

.content-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    border: 1px solid #eee;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.status {
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    text-align: center;
    font-weight: bold;
}

.status.pending {
    background: #ffeaa7;
    color: #d63031;
}

.status.processing {
    background: #74b9ff;
    color: white;
}

.status.completed {
    background: #00b894;
    color: white;
}

.status.failed {
    background: #d63031;
    color: white;
}

.status.skipped {
    background: #636e72;
    color: white;
}

.error {
    color: #d63031;
    background: #ffe0e0;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
    font-size: 14px;
}

.speaker-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #333;
}

.retry-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* Demo Admin Mood Sections */
.mood-section {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.mood-section:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.mood-section h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.2em;
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
}

.mood-section .form-group {
    margin-bottom: 15px;
}

.mood-section .form-group:last-child {
    margin-bottom: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    .user-input,
    .feed-section,
    .admin-section {
        padding: 20px;
    }

    .mood-buttons {
        flex-direction: column;
        align-items: center;
    }

    .speaker-selection {
        flex-direction: column;
    }

    .status-grid {
        grid-template-columns: 1fr;
    }

    /* Video feed responsive */
    .videos-container {
        grid-template-columns: 1fr;
        gap: 14px;
        padding: 12px 0;
    }

    .video-card video {
        max-height: 250px;
        min-height: 160px;
    }

    .video-info {
        padding: 10px 14px;
    }

    .video-info h4 {
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .videos-container {
        gap: 12px;
        grid-template-columns: 1fr;
    }

    .video-card video {
        max-height: 220px;
        min-height: 140px;
    }

    .video-card {
        border-radius: 10px;
    }

    .video-info {
        padding: 8px 12px;
    }

    .video-info h4 {
        font-size: 14px;
    }

    .video-info .view-count {
        font-size: 12px;
    }
}
