-- Create speakers table
CREATE TABLE IF NOT EXISTS speakers (
    id SERIAL PRIMARY KEY,
    full_name TEXT NOT NULL,
    real_video_sample_url TEXT,
    downloaded_real_audio_url TEXT,
    fal_custom_voice_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create moods table
CREATE TABLE IF NOT EXISTS moods (
    id SERIAL PRIMARY KEY,
    mood_name TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create speaker_mood_content table
CREATE TABLE IF NOT EXISTS speaker_mood_content (
    id SERIAL PRIMARY KEY,
    speaker_id INTEGER REFERENCES speakers(id) ON DELETE CASCADE,
    mood_id INTEGER REFERENCES moods(id) ON DELETE CASCADE,
    admin_provided_ai_video_sample_url TEXT,
    generated_speech_script TEXT,
    generated_ai_audio_url TEXT,
    final_ai_video_url TEXT,
    is_precomputed BOOLEAN DEFAULT FALSE,
    status_audio_extraction TEXT DEFAULT 'pending',
    status_voice_clone TEXT DEFAULT 'pending',
    status_script_generation TEXT DEFAULT 'pending',
    status_ai_audio_generation TEXT DEFAULT 'pending',
    status_final_video_generation TEXT DEFAULT 'pending',
    error_message TEXT,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (speaker_id, mood_id)
);

-- Insert default moods
INSERT INTO moods (mood_name) VALUES ('Happy') ON CONFLICT (mood_name) DO NOTHING;
INSERT INTO moods (mood_name) VALUES ('Calm') ON CONFLICT (mood_name) DO NOTHING;
INSERT INTO moods (mood_name) VALUES ('Motivational') ON CONFLICT (mood_name) DO NOTHING;
