#!/usr/bin/env python3
"""
Quick setup script to create tables and add demo data
"""

import os
import requests
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

# Initialize Supabase client
url: str = os.environ.get("SUPABASE_PROJECT_URL")
key: str = os.environ.get("SUPABASE_API_KEY")
supabase: Client = create_client(url, key)

def setup_everything():
    """Set up tables and demo data"""
    
    print("Setting up database and demo data...")
    
    # First, let's try to create moods directly and see what happens
    moods = ['Happy', 'Calm', 'Motivational']
    
    print("Creating moods...")
    for mood in moods:
        try:
            result = supabase.table('moods').insert({'mood_name': mood}).execute()
            print(f"✓ Created mood: {mood}")
        except Exception as e:
            print(f"✗ Error creating mood {mood}: {e}")
    
    # Try to create a test speaker
    print("\nCreating test speaker...")
    try:
        speaker_result = supabase.table('speakers').insert({
            'full_name': '<PERSON>',
            'fal_custom_voice_id': 'demo_gary_voice_id'
        }).execute()
        
        speaker_id = speaker_result.data[0]['id']
        print(f"✓ Created speaker: <PERSON>rchuk (ID: {speaker_id})")
        
        # Get mood IDs
        moods_response = supabase.table('moods').select('*').execute()
        mood_map = {mood['mood_name']: mood['id'] for mood in moods_response.data}
        
        # Add demo content
        demo_content = [
            {
                'mood': 'Happy',
                'script': 'Every single day is a GIFT! You wake up, you have another shot at greatness. Stop dwelling on yesterday, stop worrying about tomorrow. TODAY is your canvas, and you get to paint it however you want. Let\'s GO!',
                'video_url': '/static/samples/gary_vaynerchuk/monday_morning.mp4'
            },
            {
                'mood': 'Motivational',
                'script': 'You think you\'re behind? You think it\'s too late? WRONG! The only thing that matters is what you do RIGHT NOW. Stop making excuses, stop waiting for permission. Your time is NOW, your moment is TODAY!',
                'video_url': '/static/samples/gary_vaynerchuk/lip-sync-video-generated.mp4'
            },
            {
                'mood': 'Calm',
                'script': 'Take a deep breath. Success isn\'t about the RUSH, it\'s about consistency. Every day you show up, every day you put in the work, you\'re building something SPECIAL. Trust the process.',
                'video_url': '/static/samples/gary_vaynerchuk/regrets_video.mp4'
            }
        ]
        
        for content in demo_content:
            mood_id = mood_map.get(content['mood'])
            if mood_id:
                try:
                    content_data = {
                        'speaker_id': speaker_id,
                        'mood_id': mood_id,
                        'generated_speech_script': content['script'],
                        'final_ai_video_url': content['video_url'],
                        'is_precomputed': True,
                        'status_audio_extraction': 'skipped',
                        'status_voice_clone': 'skipped',
                        'status_script_generation': 'completed',
                        'status_ai_audio_generation': 'skipped',
                        'status_final_video_generation': 'completed'
                    }
                    
                    supabase.table('speaker_mood_content').insert(content_data).execute()
                    print(f"✓ Added {content['mood']} content")
                except Exception as e:
                    print(f"✗ Error adding {content['mood']} content: {e}")
        
    except Exception as e:
        print(f"✗ Error creating speaker: {e}")
    
    print("\nSetup complete! Try refreshing the web page.")

if __name__ == "__main__":
    setup_everything()
