#!/usr/bin/env python3
"""
Script to create database tables using Supabase management API
"""

import requests
import os
from dotenv import load_dotenv

load_dotenv()

# Supabase project details
PROJECT_REF = "yxdkdztxvjtrxmzosbkg"
SUPABASE_URL = os.environ.get("SUPABASE_PROJECT_URL")
SUPABASE_KEY = os.environ.get("SUPABASE_API_KEY")

def create_tables():
    """Create tables using direct SQL execution"""
    
    # SQL to create all tables
    sql_commands = [
        """
        CREATE TABLE IF NOT EXISTS speakers (
            id SERIAL PRIMARY KEY,
            full_name TEXT NOT NULL,
            real_video_sample_url TEXT,
            downloaded_real_audio_url TEXT,
            downloaded_real_video_url TEXT,
            fal_custom_voice_id TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS moods (
            id SERIAL PRIMARY KEY,
            mood_name TEXT NOT NULL UNIQUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS speaker_mood_content (
            id SERIAL PRIMARY KEY,
            speaker_id INTEGER REFERENCES speakers(id) ON DELETE CASCADE,
            mood_id INTEGER REFERENCES moods(id) ON DELETE CASCADE,
            admin_provided_ai_video_sample_url TEXT,
            generated_speech_script TEXT,
            generated_ai_audio_url TEXT,
            final_ai_video_url TEXT,
            is_precomputed BOOLEAN DEFAULT FALSE,
            status_audio_extraction TEXT DEFAULT 'pending',
            status_voice_clone TEXT DEFAULT 'pending',
            status_script_generation TEXT DEFAULT 'pending',
            status_ai_audio_generation TEXT DEFAULT 'pending',
            status_final_video_generation TEXT DEFAULT 'pending',
            error_message TEXT,
            view_count INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            UNIQUE (speaker_id, mood_id)
        );
        """,
        """
        INSERT INTO moods (mood_name) VALUES ('Happy') ON CONFLICT (mood_name) DO NOTHING;
        """,
        """
        INSERT INTO moods (mood_name) VALUES ('Calm') ON CONFLICT (mood_name) DO NOTHING;
        """,
        """
        INSERT INTO moods (mood_name) VALUES ('Motivational') ON CONFLICT (mood_name) DO NOTHING;
        """
    ]
    
    print("Creating database tables...")
    
    # Execute each SQL command
    for i, sql in enumerate(sql_commands):
        try:
            print(f"Executing command {i+1}/{len(sql_commands)}...")
            
            # Use Supabase REST API to execute SQL
            headers = {
                'apikey': SUPABASE_KEY,
                'Authorization': f'Bearer {SUPABASE_KEY}',
                'Content-Type': 'application/json'
            }
            
            # For now, let's just print the SQL and ask user to run it manually
            print(f"SQL Command {i+1}:")
            print(sql.strip())
            print("-" * 50)
            
        except Exception as e:
            print(f"Error executing command {i+1}: {e}")
    
    print("\nPlease copy and paste these SQL commands into your Supabase SQL Editor:")
    print("1. Go to https://supabase.com/dashboard/project/yxdkdztxvjtrxmzosbkg/sql")
    print("2. Paste each SQL command above and run them")
    print("3. Then run the demo content script")

if __name__ == "__main__":
    create_tables()
