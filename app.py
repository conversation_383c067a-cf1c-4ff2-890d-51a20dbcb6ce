import os
import asyncio
from flask import Flask, render_template, request, jsonify, redirect, url_for, session
from dotenv import load_dotenv
from database import get_supabase_client, create_tables
from pipeline import trigger_pipeline
import threading

load_dotenv()

app = Flask(__name__)
app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'your-secret-key-here')

# Initialize database
supabase = get_supabase_client()

# Admin password from environment
ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin123')

@app.route('/')
def index():
    """User-facing feed page"""
    return render_template('index.html')

@app.route('/favicon.ico')
def favicon():
    """Handle favicon requests"""
    return '', 204  # No content

@app.route('/admin')
def admin():
    """Admin panel for adding speakers"""
    if not session.get('admin_authenticated'):
        return render_template('admin_login.html', route='admin')
    
    # Get all moods for the form
    moods_response = supabase.table('moods').select('*').execute()
    moods = moods_response.data
    
    # Get all speakers with their content status
    speakers_response = supabase.table('speakers').select('*').execute()
    speakers = speakers_response.data
    
    # Get content status for each speaker
    for speaker in speakers:
        content_response = supabase.table('speaker_mood_content').select('*').eq('speaker_id', speaker['id']).execute()
        speaker['content'] = content_response.data
    
    return render_template('admin.html', moods=moods, speakers=speakers)

@app.route('/demo-admin')
def demo_admin():
    """Demo admin panel for pre-computed content"""
    if not session.get('demo_admin_authenticated'):
        return render_template('admin_login.html', route='demo-admin')
    
    # Get all moods and speakers
    moods_response = supabase.table('moods').select('*').execute()
    moods = moods_response.data
    
    speakers_response = supabase.table('speakers').select('*').execute()
    speakers = speakers_response.data
    
    return render_template('demo_admin.html', moods=moods, speakers=speakers)

@app.route('/admin-login', methods=['POST'])
def admin_login():
    """Handle admin login"""
    password = request.form.get('password')
    route = request.form.get('route')
    
    if password == ADMIN_PASSWORD:
        if route == 'admin':
            session['admin_authenticated'] = True
            return redirect(url_for('admin'))
        elif route == 'demo-admin':
            session['demo_admin_authenticated'] = True
            return redirect(url_for('demo_admin'))
    
    return render_template('admin_login.html', route=route, error='Invalid password')

@app.route('/add-speaker', methods=['POST'])
def add_speaker():
    """Add new speaker via admin panel"""
    if not session.get('admin_authenticated'):
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        full_name = request.form.get('full_name')
        real_video_url = request.form.get('real_video_url')

        # Validate required fields
        if not full_name or not real_video_url:
            return jsonify({'error': 'Full name and YouTube video URL are required'}), 400

        # Get all moods and check for at least one AI video sample
        moods_response = supabase.table('moods').select('*').execute()
        moods = moods_response.data

        ai_video_samples = []
        for mood in moods:
            ai_video_url = request.form.get(f'ai_video_url_{mood["id"]}')
            if ai_video_url:
                ai_video_samples.append((mood['id'], ai_video_url))

        # Validate that at least one AI video sample is provided
        if not ai_video_samples:
            return jsonify({'error': 'Please provide at least one AI video sample for any mood'}), 400

        # Create speaker record
        speaker_data = {
            'full_name': full_name,
            'real_video_sample_url': real_video_url,
            'fal_custom_voice_id': ''  # Will be populated by pipeline
        }

        speaker_response = supabase.table('speakers').insert(speaker_data).execute()
        speaker_id = speaker_response.data[0]['id']

        # Create speaker_mood_content records only for moods with AI video samples
        for mood_id, ai_video_url in ai_video_samples:
            content_data = {
                'speaker_id': speaker_id,
                'mood_id': mood_id,
                'admin_provided_ai_video_sample_url': ai_video_url,
                'is_precomputed': False
            }

            supabase.table('speaker_mood_content').insert(content_data).execute()

        # Trigger pipeline in background
        threading.Thread(target=trigger_pipeline, args=(speaker_id,)).start()

        message = f'Speaker added successfully! Pipeline started for {len(ai_video_samples)} mood(s).'
        return jsonify({'success': True, 'message': message})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/add-demo-content', methods=['POST'])
def add_demo_content():
    """Add pre-computed content via demo admin panel for multiple moods"""
    if not session.get('demo_admin_authenticated'):
        return jsonify({'error': 'Unauthorized'}), 401

    try:
        speaker_id = request.form.get('speaker_id')
        new_speaker_name = request.form.get('new_speaker_name')
        fal_voice_id = request.form.get('fal_voice_id', '')
        ai_video_url = request.form.get('ai_video_url', '')

        # Validate that either speaker_id or new_speaker_name is provided
        if not speaker_id and not new_speaker_name:
            return jsonify({'error': 'Either select an existing speaker or enter a new speaker name'}), 400

        # Create new speaker if needed
        if not speaker_id and new_speaker_name:
            speaker_data = {
                'full_name': new_speaker_name
            }
            # Only add voice ID if provided
            if fal_voice_id:
                speaker_data['fal_custom_voice_id'] = fal_voice_id

            speaker_response = supabase.table('speakers').insert(speaker_data).execute()
            speaker_id = speaker_response.data[0]['id']
        elif speaker_id and fal_voice_id:
            # Update existing speaker's voice ID if provided
            supabase.table('speakers').update({'fal_custom_voice_id': fal_voice_id}).eq('id', speaker_id).execute()

        # Get all moods to process
        moods_response = supabase.table('moods').select('*').execute()
        moods = moods_response.data

        created_count = 0
        updated_count = 0

        # Process each mood
        for mood in moods:
            mood_id = mood['id']
            script = request.form.get(f'script_{mood_id}')
            ai_audio_url = request.form.get(f'ai_audio_url_{mood_id}', '')
            final_video_url = request.form.get(f'final_video_url_{mood_id}')

            # Skip if no final video URL provided for this mood
            if not final_video_url:
                continue

            # Create or update speaker_mood_content record
            content_data = {
                'speaker_id': int(speaker_id),
                'mood_id': int(mood_id),
                'final_ai_video_url': final_video_url,
                'is_precomputed': True,
                'status_final_video_generation': 'completed'
            }

            # Add optional fields only if provided
            if ai_video_url:
                content_data['admin_provided_ai_video_sample_url'] = ai_video_url
                content_data['status_audio_extraction'] = 'skipped'

            if script:
                content_data['generated_speech_script'] = script
                content_data['status_script_generation'] = 'completed'
            else:
                content_data['status_script_generation'] = 'skipped'

            if ai_audio_url:
                content_data['generated_ai_audio_url'] = ai_audio_url
                content_data['status_ai_audio_generation'] = 'completed'
            else:
                content_data['status_ai_audio_generation'] = 'skipped'

            if fal_voice_id:
                content_data['status_voice_clone'] = 'completed'
            else:
                content_data['status_voice_clone'] = 'skipped'

            # Try to insert, if conflict then update
            try:
                supabase.table('speaker_mood_content').insert(content_data).execute()
                created_count += 1
            except:
                # Update existing record
                supabase.table('speaker_mood_content').update(content_data).eq('speaker_id', speaker_id).eq('mood_id', mood_id).execute()
                updated_count += 1

        message = f'Demo content processed successfully! Created: {created_count}, Updated: {updated_count}'
        return jsonify({'success': True, 'message': message})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# API Endpoints
@app.route('/api/moods')
def api_moods():
    """Get all moods"""
    try:
        response = supabase.table('moods').select('*').execute()
        return jsonify(response.data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/feed')
def api_feed():
    """Get feed content for a specific mood"""
    try:
        mood_id = request.args.get('mood_id')
        if not mood_id:
            return jsonify({'error': 'mood_id is required'}), 400
        
        # Query speaker_mood_content with speaker info
        response = supabase.table('speaker_mood_content').select(
            'id, final_ai_video_url, view_count, speakers(full_name)'
        ).eq('mood_id', mood_id).not_.is_('final_ai_video_url', 'null').neq('final_ai_video_url', '').order('view_count', desc=True).execute()
        
        # Format response
        feed_data = []
        for item in response.data:
            if item['final_ai_video_url']:
                feed_data.append({
                    'content_id': item['id'],
                    'speaker_name': item['speakers']['full_name'],
                    'final_video_url': item['final_ai_video_url'],
                    'view_count': item['view_count']
                })
        
        return jsonify(feed_data)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/video/increment_view/<int:content_id>', methods=['POST'])
def increment_view(content_id):
    """Increment view count for a video"""
    try:
        # Get current view count
        response = supabase.table('speaker_mood_content').select('view_count').eq('id', content_id).execute()
        if not response.data:
            return jsonify({'error': 'Content not found'}), 404

        current_count = response.data[0]['view_count']
        new_count = current_count + 1

        # Update view count
        supabase.table('speaker_mood_content').update({'view_count': new_count}).eq('id', content_id).execute()

        return jsonify({'success': True, 'new_count': new_count})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/retry-pipeline/<int:speaker_id>', methods=['POST'])
def retry_pipeline_endpoint(speaker_id):
    """Retry pipeline for a specific speaker"""
    try:
        # Reset failed statuses
        supabase.table('speaker_mood_content').update({
            'status_audio_extraction': 'pending',
            'status_voice_clone': 'pending',
            'status_script_generation': 'pending',
            'status_ai_audio_generation': 'pending',
            'status_final_video_generation': 'pending',
            'error_message': None
        }).eq('speaker_id', speaker_id).execute()

        # Reset speaker audio URL if it failed
        supabase.table('speakers').update({
            'downloaded_real_audio_url': None
        }).eq('id', speaker_id).execute()

        # Trigger pipeline in background
        import threading
        from pipeline import trigger_pipeline
        threading.Thread(target=trigger_pipeline, args=(speaker_id,)).start()

        return jsonify({'success': True, 'message': 'Pipeline restarted'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Initialize database tables (for local development)
if __name__ == '__main__':
    # Create tables on startup
    create_tables()

    # Get port from environment variable
    import os
    port = int(os.environ.get('PORT', 5000))

    # For production, disable debug mode
    debug_mode = os.environ.get('VERCEL_ENV') != 'production' and os.environ.get('RAILWAY_ENVIRONMENT') != 'production'

    app.run(debug=debug_mode, host='0.0.0.0', port=port)

# For Vercel deployment
else:
    # Skip database initialization for serverless - use existing DB
    print("Serverless mode: Using existing database")
