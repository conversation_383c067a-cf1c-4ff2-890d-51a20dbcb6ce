<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Feel Better</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>Admin Panel</h1>
            <p>Add new speakers for automated content generation</p>
        </header>

        <!-- Add Speaker Form -->
        <div class="admin-section">
            <h2>Add New Speaker</h2>
            <form id="add-speaker-form">
                <div class="form-group">
                    <label for="full_name">Full Name:</label>
                    <input type="text" id="full_name" name="full_name" required>
                </div>
                
                <div class="form-group">
                    <label for="real_video_url">YouTube Video URL (for voice cloning):</label>
                    <input type="url" id="real_video_url" name="real_video_url" required>
                    <small>Required - Max 5 minutes for voice cloning</small>
                </div>

                <h3>AI Video Samples for Each Mood</h3>
                <p><small>Add AI video samples for the moods you want to generate content for. You can add just one mood (e.g., grandma for motivation only) or multiple moods. Leave empty for moods you don't want to generate.</small></p>

                {% for mood in moods %}
                <div class="mood-section">
                    <h4>{{ mood.mood_name }}</h4>
                    <div class="form-group">
                        <label for="ai_video_url_{{ mood.id }}">AI Video Sample for {{ mood.mood_name }} (Optional):</label>
                        <input type="url" id="ai_video_url_{{ mood.id }}" name="ai_video_url_{{ mood.id }}" placeholder="Visual source for lip-sync">
                    </div>
                </div>
                {% endfor %}
                
                <button type="submit">Add Speaker</button>
            </form>
        </div>

        <!-- Existing Speakers -->
        <div class="admin-section">
            <h2>Existing Speakers</h2>
            <div class="speakers-list">
                {% for speaker in speakers %}
                <div class="speaker-card">
                    <h3>{{ speaker.full_name }}</h3>
                    <p><strong>Voice ID:</strong> {{ speaker.fal_custom_voice_id or 'Not generated yet' }}</p>

                    {% if speaker.content %}
                    <h4>Content Status:</h4>
                    <div class="content-status">
                        {% for content in speaker.content %}
                        <div class="content-item">
                            <p><strong>Mood:</strong> {{ content.mood_id }}</p>
                            <div class="status-grid">
                                <span class="status {{ content.status_audio_extraction }}">Audio: {{ content.status_audio_extraction }}</span>
                                <span class="status {{ content.status_voice_clone }}">Voice: {{ content.status_voice_clone }}</span>
                                <span class="status {{ content.status_script_generation }}">Script: {{ content.status_script_generation }}</span>
                                <span class="status {{ content.status_ai_audio_generation }}">Audio Gen: {{ content.status_ai_audio_generation }}</span>
                                <span class="status {{ content.status_final_video_generation }}">Video Gen: {{ content.status_final_video_generation }}</span>
                            </div>
                            {% if content.error_message %}
                            <p class="error">Error: {{ content.error_message }}</p>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="speaker-actions">
                        <button class="retry-btn" onclick="retryPipeline({{ speaker.id }})">Retry Pipeline</button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <script>
        document.getElementById('add-speaker-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Validate that at least one AI video sample is provided
            const moodIds = [1, 2, 3]; // Happy, Calm, Motivational
            let hasAtLeastOneVideo = false;

            for (const moodId of moodIds) {
                const aiVideoUrl = document.getElementById(`ai_video_url_${moodId}`).value.trim();
                if (aiVideoUrl) {
                    hasAtLeastOneVideo = true;
                    break;
                }
            }

            if (!hasAtLeastOneVideo) {
                alert('Please provide at least one AI video sample for any mood. A speaker can be associated with just one mood if that\'s what suits them best.');
                return;
            }

            const formData = new FormData(this);

            try {
                const response = await fetch('/add-speaker', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    alert('Speaker added successfully! Pipeline started.');
                    location.reload();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        });

        async function retryPipeline(speakerId) {
            if (!confirm('Are you sure you want to retry the pipeline for this speaker?')) {
                return;
            }

            try {
                const response = await fetch(`/retry-pipeline/${speakerId}`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    alert('Pipeline restarted successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }
    </script>
</body>
</html>
