<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Admin Panel - Feel Better</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>Demo Admin Panel</h1>
            <p>Add pre-computed content for demo purposes</p>
        </header>

        <div class="admin-section">
            <h2>Add Pre-computed Content</h2>
            <form id="demo-content-form">
                <div class="form-group">
                    <label>Speaker:</label>
                    <div class="speaker-selection">
                        <select id="speaker_id" name="speaker_id">
                            <option value="">Select existing speaker</option>
                            {% for speaker in speakers %}
                            <option value="{{ speaker.id }}">{{ speaker.full_name }}</option>
                            {% endfor %}
                        </select>
                        <span>OR</span>
                        <input type="text" id="new_speaker_name" name="new_speaker_name" placeholder="New speaker name">
                    </div>
                    <small>Either select existing speaker or enter new speaker name (required)</small>
                </div>

                <div class="form-group">
                    <label for="fal_voice_id">Fal Custom Voice ID (Optional):</label>
                    <input type="text" id="fal_voice_id" name="fal_voice_id">
                    <small>Optional - for voice cloning functionality</small>
                </div>

                <div class="form-group">
                    <label for="ai_video_url">AI Video Sample URL (Optional):</label>
                    <input type="url" id="ai_video_url" name="ai_video_url">
                    <small>Optional - visual source for lip-sync (same for all moods)</small>
                </div>

                <h3>Videos for Each Mood</h3>
                <p><small>Add final video URLs for the moods this speaker is suitable for. You can add just one mood (e.g., grandma for motivation only) or multiple moods. Leave empty for moods that don't apply.</small></p>

                {% for mood in moods %}
                <div class="mood-section">
                    <h4>{{ mood.mood_name }}</h4>

                    <div class="form-group">
                        <label for="script_{{ mood.id }}">Generated Speech Script:</label>
                        <textarea id="script_{{ mood.id }}" name="script_{{ mood.id }}" rows="3" placeholder="30 seconds max (70-80 words)"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="ai_audio_url_{{ mood.id }}">Generated AI Audio URL (Optional):</label>
                        <input type="url" id="ai_audio_url_{{ mood.id }}" name="ai_audio_url_{{ mood.id }}" placeholder="Leave empty if providing final video directly">
                    </div>

                    <div class="form-group">
                        <label for="final_video_url_{{ mood.id }}">Final AI Video URL:</label>
                        <input type="url" id="final_video_url_{{ mood.id }}" name="final_video_url_{{ mood.id }}" placeholder="Final lip-synced video for {{ mood.mood_name }}">
                    </div>
                </div>
                {% endfor %}

                <button type="submit">Add Demo Content for All Moods</button>
            </form>
        </div>
    </div>

    <script>
        document.getElementById('demo-content-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Validate speaker selection
            const speakerId = document.getElementById('speaker_id').value;
            const newSpeakerName = document.getElementById('new_speaker_name').value.trim();

            if (!speakerId && !newSpeakerName) {
                alert('Please either select an existing speaker or enter a new speaker name.');
                return;
            }

            // Validate that at least one mood has a final video URL
            const moodIds = [1, 2, 3]; // Happy, Calm, Motivational
            let hasAtLeastOneVideo = false;

            for (const moodId of moodIds) {
                const finalVideoUrl = document.getElementById(`final_video_url_${moodId}`).value.trim();
                if (finalVideoUrl) {
                    hasAtLeastOneVideo = true;
                    break;
                }
            }

            if (!hasAtLeastOneVideo) {
                alert('Please provide at least one final video URL for any mood. A speaker can be associated with just one mood if that\'s what suits them best.');
                return;
            }

            const formData = new FormData(this);

            try {
                const response = await fetch('/add-demo-content', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    alert(result.message || 'Demo content added successfully!');
                    this.reset();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        });

        // Clear new speaker name when existing speaker is selected
        document.getElementById('speaker_id').addEventListener('change', function() {
            if (this.value) {
                document.getElementById('new_speaker_name').value = '';
            }
        });

        // Clear speaker selection when new name is entered
        document.getElementById('new_speaker_name').addEventListener('input', function() {
            if (this.value.trim()) {
                document.getElementById('speaker_id').value = '';
            }
        });
    </script>
</body>
</html>
