#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add demo content to the database.
This will add some sample speakers and content for testing.
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

# Initialize Supabase client
url: str = os.environ.get("SUPABASE_PROJECT_URL")
key: str = os.environ.get("SUPABASE_API_KEY")
supabase: Client = create_client(url, key)

def add_demo_content():
    """Add demo content for testing"""
    
    print("Adding demo content...")
    
    # Sample demo content using actual files
    demo_speakers = [
        {
            'full_name': '<PERSON>',
            'fal_custom_voice_id': 'demo_gary_voice_id',
            'content': [
                {
                    'mood': 'Happy',
                    'script': 'Every single day is a GIFT! You wake up, you have another shot at greatness. Stop dwelling on yesterday, stop worrying about tomorrow. TODAY is your canvas, and you get to paint it however you want. Let\'s GO!',
                    'video_url': '/static/samples/gary_vaynerchuk/monday_morning.mp4'
                },
                {
                    'mood': 'Motivational',
                    'script': 'You think you\'re behind? You think it\'s too late? WRONG! The only thing that matters is what you do RIGHT NOW. Stop making excuses, stop waiting for permission. Your time is NOW, your moment is TODAY!',
                    'video_url': '/static/samples/gary_vaynerchuk/lip-sync-video-generated.mp4'
                },
                {
                    'mood': 'Calm',
                    'script': 'Take a deep breath. Success isn\'t about the RUSH, it\'s about consistency. Every day you show up, every day you put in the work, you\'re building something SPECIAL. Trust the process.',
                    'video_url': '/static/samples/gary_vaynerchuk/regrets_video.mp4'
                }
            ]
        }
    ]
    
    # Get mood IDs
    moods_response = supabase.table('moods').select('*').execute()
    mood_map = {mood['mood_name']: mood['id'] for mood in moods_response.data}
    
    for speaker_data in demo_speakers:
        try:
            # Create speaker
            speaker_response = supabase.table('speakers').insert({
                'full_name': speaker_data['full_name'],
                'fal_custom_voice_id': speaker_data['fal_custom_voice_id']
            }).execute()
            
            speaker_id = speaker_response.data[0]['id']
            print(f"Created speaker: {speaker_data['full_name']} (ID: {speaker_id})")
            
            # Add content for each mood
            for content in speaker_data['content']:
                mood_id = mood_map.get(content['mood'])
                if mood_id:
                    content_data = {
                        'speaker_id': speaker_id,
                        'mood_id': mood_id,
                        'generated_speech_script': content['script'],
                        'final_ai_video_url': content['video_url'],
                        'is_precomputed': True,
                        'status_audio_extraction': 'skipped',
                        'status_voice_clone': 'skipped',
                        'status_script_generation': 'completed',
                        'status_ai_audio_generation': 'skipped',
                        'status_final_video_generation': 'completed'
                    }
                    
                    supabase.table('speaker_mood_content').insert(content_data).execute()
                    print(f"Added {content['mood']} content for {speaker_data['full_name']}")
        
        except Exception as e:
            print(f"Error adding speaker {speaker_data['full_name']}: {e}")
    
    print("Demo content added successfully!")

if __name__ == "__main__":
    add_demo_content()
